const express = require('express');
const router = express.Router();
const User = require('../src/models/User');
const ClothingItem = require('../src/models/ClothingItem');
const BulkDonation = require('../models/BulkDonation');
const TokenPurchase = require('../models/TokenPurchase');
const CharityPartner = require('../models/CharityPartner');
const RecyclingCenter = require('../models/RecyclingCenter');
const auth = require('../middleware/auth');

// Admin middleware
const adminAuth = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Admin privileges required.'
    });
  }
  next();
};

// Get admin dashboard statistics
router.get('/stats', auth, adminAuth, async (req, res) => {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    // User statistics
    const userStats = await User.aggregate([
      {
        $facet: {
          total: [{ $count: "count" }],
          active: [{ $match: { isActive: true } }, { $count: "count" }],
          verified: [{ $match: { isVerified: true } }, { $count: "count" }],
          newThisMonth: [
            { $match: { createdAt: { $gte: startOfMonth } } },
            { $count: "count" }
          ]
        }
      }
    ]);

    const users = {
      total: userStats[0].total[0]?.count || 0,
      active: userStats[0].active[0]?.count || 0,
      verified: userStats[0].verified[0]?.count || 0,
      newThisMonth: userStats[0].newThisMonth[0]?.count || 0
    };

    // Clothing statistics
    const clothingStats = await ClothingItem.aggregate([
      {
        $facet: {
          total: [{ $count: "count" }],
          available: [{ $match: { isAvailable: true } }, { $count: "count" }],
          swapped: [{ $match: { status: 'swapped' } }, { $count: "count" }],
          donated: [{ $match: { status: 'donated' } }, { $count: "count" }]
        }
      }
    ]);

    const clothing = {
      total: clothingStats[0].total[0]?.count || 0,
      available: clothingStats[0].available[0]?.count || 0,
      swapped: clothingStats[0].swapped[0]?.count || 0,
      donated: clothingStats[0].donated[0]?.count || 0
    };

    // Donation statistics
    const donationStats = await BulkDonation.aggregate([
      {
        $match: { type: 'charity' }
      },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          totalWeight: { $sum: '$weight' },
          totalValue: { $sum: '$tokensEarned' },
          pendingPickups: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          }
        }
      }
    ]);

    const donations = donationStats[0] || {
      total: 0,
      totalWeight: 0,
      totalValue: 0,
      pendingPickups: 0
    };

    // Recycling statistics
    const recyclingStats = await BulkDonation.aggregate([
      {
        $match: { type: 'recycling' }
      },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          totalWeight: { $sum: '$weight' },
          co2Saved: { $sum: '$environmentalImpact.co2Saved' },
          pendingProcessing: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          }
        }
      }
    ]);

    const recycling = recyclingStats[0] || {
      total: 0,
      totalWeight: 0,
      co2Saved: 0,
      pendingProcessing: 0
    };

    // Token statistics
    const tokenStats = await TokenPurchase.aggregate([
      {
        $match: { status: 'completed' }
      },
      {
        $group: {
          _id: null,
          totalIssued: { $sum: '$totalTokens' },
          totalPurchased: { $sum: '$totalTokens' },
          revenue: { $sum: '$amountKES' }
        }
      }
    ]);

    // Get total tokens spent from user transactions
    const spentTokensResult = await User.aggregate([
      {
        $group: {
          _id: null,
          totalSpent: { $sum: '$totalTokensSpent' }
        }
      }
    ]);

    const tokens = {
      totalIssued: (tokenStats[0]?.totalIssued || 0) + users.total * 100, // Include welcome bonuses
      totalSpent: spentTokensResult[0]?.totalSpent || 0,
      totalPurchased: tokenStats[0]?.totalPurchased || 0,
      revenue: tokenStats[0]?.revenue || 0
    };

    // Partner statistics
    const charityCount = await CharityPartner.countDocuments({ isActive: true });
    const recyclingCount = await RecyclingCenter.countDocuments({ isActive: true });
    const activePartnerships = charityCount + recyclingCount;

    const partners = {
      charities: charityCount,
      recyclingCenters: recyclingCount,
      activePartnerships
    };

    res.json({
      success: true,
      data: {
        users,
        clothing,
        donations,
        recycling,
        tokens,
        partners
      }
    });

  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get recent activity
router.get('/recent-activity', auth, adminAuth, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;

    // Get recent user registrations
    const recentUsers = await User.find({ isActive: true })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('firstName lastName createdAt');

    // Get recent clothing listings
    const recentClothing = await ClothingItem.find()
      .populate('owner', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title owner createdAt');

    // Get recent donations
    const recentDonations = await BulkDonation.find()
      .populate('user', 'firstName lastName')
      .sort({ createdAt: -1 })
      .limit(5)
      .select('type weight user createdAt status');

    // Get recent token purchases
    const recentPurchases = await TokenPurchase.find({ status: 'completed' })
      .populate('user', 'firstName lastName')
      .sort({ completedAt: -1 })
      .limit(5)
      .select('totalTokens user completedAt');

    // Combine and format activities
    const activities = [];

    recentUsers.forEach(user => {
      activities.push({
        _id: user._id,
        type: 'user_registration',
        description: `New user registered`,
        user: {
          firstName: user.firstName,
          lastName: user.lastName
        },
        createdAt: user.createdAt,
        status: 'completed'
      });
    });

    recentClothing.forEach(item => {
      activities.push({
        _id: item._id,
        type: 'clothing_listed',
        description: `Listed "${item.title}"`,
        user: {
          firstName: item.owner.firstName,
          lastName: item.owner.lastName
        },
        createdAt: item.createdAt,
        status: 'completed'
      });
    });

    recentDonations.forEach(donation => {
      activities.push({
        _id: donation._id,
        type: donation.type === 'charity' ? 'donation' : 'recycling',
        description: `${donation.type === 'charity' ? 'Donated' : 'Recycled'} ${donation.weight}kg`,
        user: {
          firstName: donation.user.firstName,
          lastName: donation.user.lastName
        },
        createdAt: donation.createdAt,
        status: donation.status
      });
    });

    recentPurchases.forEach(purchase => {
      activities.push({
        _id: purchase._id,
        type: 'token_purchase',
        description: `Purchased ${purchase.totalTokens} tokens`,
        user: {
          firstName: purchase.user.firstName,
          lastName: purchase.user.lastName
        },
        createdAt: purchase.completedAt,
        status: 'completed'
      });
    });

    // Sort by date and limit
    activities.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    const limitedActivities = activities.slice(0, limit);

    res.json({
      success: true,
      data: limitedActivities
    });

  } catch (error) {
    console.error('Error fetching recent activity:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get platform analytics
router.get('/analytics', auth, adminAuth, async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 30;
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    // Daily user registrations
    const userTrends = await User.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Daily clothing listings
    const listingTrends = await ClothingItem.aggregate([
      { $match: { createdAt: { $gte: startDate } } },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Daily token purchases
    const purchaseTrends = await TokenPurchase.aggregate([
      { 
        $match: { 
          completedAt: { $gte: startDate },
          status: 'completed'
        } 
      },
      {
        $group: {
          _id: {
            year: { $year: '$completedAt' },
            month: { $month: '$completedAt' },
            day: { $dayOfMonth: '$completedAt' }
          },
          count: { $sum: 1 },
          revenue: { $sum: '$amountKES' },
          tokens: { $sum: '$totalTokens' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Top counties by user count
    const topCounties = await User.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: '$location.county',
          userCount: { $sum: 1 }
        }
      },
      { $sort: { userCount: -1 } },
      { $limit: 10 }
    ]);

    res.json({
      success: true,
      data: {
        userTrends,
        listingTrends,
        purchaseTrends,
        topCounties,
        timeframe: `${days} days`
      }
    });

  } catch (error) {
    console.error('Error fetching analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get system health metrics
router.get('/health', auth, adminAuth, async (req, res) => {
  try {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    // Check for failed transactions in last 24 hours
    const failedPurchases = await TokenPurchase.countDocuments({
      status: 'failed',
      createdAt: { $gte: last24Hours }
    });

    // Check for pending donations older than 7 days
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const staleDonations = await BulkDonation.countDocuments({
      status: 'pending',
      createdAt: { $lte: sevenDaysAgo }
    });

    // Check for inactive users with recent activity
    const inactiveUsers = await User.countDocuments({
      isActive: false,
      lastActive: { $gte: last24Hours }
    });

    // System alerts
    const alerts = [];
    
    if (failedPurchases > 10) {
      alerts.push({
        type: 'error',
        message: `${failedPurchases} failed token purchases in last 24 hours`,
        priority: 'high'
      });
    }

    if (staleDonations > 0) {
      alerts.push({
        type: 'warning',
        message: `${staleDonations} donations pending for over 7 days`,
        priority: 'medium'
      });
    }

    if (inactiveUsers > 5) {
      alerts.push({
        type: 'info',
        message: `${inactiveUsers} inactive users with recent activity`,
        priority: 'low'
      });
    }

    res.json({
      success: true,
      data: {
        status: alerts.length === 0 ? 'healthy' : 'attention_needed',
        alerts,
        metrics: {
          failedPurchases,
          staleDonations,
          inactiveUsers
        },
        lastChecked: now
      }
    });

  } catch (error) {
    console.error('Error checking system health:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
