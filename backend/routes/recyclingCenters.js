const express = require('express');
const router = express.Router();
const RecyclingCenter = require('../models/RecyclingCenter');
const auth = require('../middleware/auth');
const { body, validationResult } = require('express-validator');

// Get all active recycling centers
router.get('/', auth, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    const county = req.query.county;
    const town = req.query.town;
    const specialty = req.query.specialty;

    let filter = { isActive: true, isVerified: true };
    
    if (county) filter['location.county'] = county;
    if (town) filter['location.town'] = town;
    if (specialty) filter.specialties = specialty;

    const centers = await RecyclingCenter.find(filter)
      .select('-adminNotes -documents -bankDetails')
      .sort({ 'rating.average': -1, 'statistics.totalRecycled': -1 })
      .skip(skip)
      .limit(limit);

    const total = await RecyclingCenter.countDocuments(filter);

    res.json({
      success: true,
      data: centers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching recycling centers:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get recycling center by ID
router.get('/:id', auth, async (req, res) => {
  try {
    const center = await RecyclingCenter.findOne({
      _id: req.params.id,
      isActive: true,
      isVerified: true
    }).select('-adminNotes -documents -bankDetails');

    if (!center) {
      return res.status(404).json({
        success: false,
        message: 'Recycling center not found'
      });
    }

    res.json({
      success: true,
      data: center
    });

  } catch (error) {
    console.error('Error fetching recycling center:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get nearby recycling centers
router.get('/nearby/:county/:town', auth, async (req, res) => {
  try {
    const { county, town } = req.params;
    const limit = parseInt(req.query.limit) || 5;

    const nearbyCenters = await RecyclingCenter.findNearby(county, town, limit);

    res.json({
      success: true,
      data: nearbyCenters
    });

  } catch (error) {
    console.error('Error fetching nearby centers:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get centers by specialty
router.get('/specialty/:specialty', auth, async (req, res) => {
  try {
    const { specialty } = req.params;
    const limit = parseInt(req.query.limit) || 10;

    const centers = await RecyclingCenter.findBySpecialty(specialty, limit);

    res.json({
      success: true,
      data: centers
    });

  } catch (error) {
    console.error('Error fetching centers by specialty:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Get top performing recycling centers
router.get('/featured/top-performers', auth, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 5;
    const topCenters = await RecyclingCenter.getTopPerformers(limit);

    res.json({
      success: true,
      data: topCenters
    });

  } catch (error) {
    console.error('Error fetching top centers:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Calculate environmental impact for a given weight
router.post('/:id/calculate-impact', auth, async (req, res) => {
  try {
    const { weight } = req.body;

    if (!weight || weight <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Valid weight is required'
      });
    }

    const center = await RecyclingCenter.findOne({
      _id: req.params.id,
      isActive: true,
      isVerified: true
    });

    if (!center) {
      return res.status(404).json({
        success: false,
        message: 'Recycling center not found'
      });
    }

    const impact = center.calculateImpact(weight);

    res.json({
      success: true,
      data: {
        weight,
        impact,
        center: {
          name: center.name,
          rewardRate: center.rewardRate,
          estimatedTokens: Math.round(weight * center.rewardRate)
        }
      }
    });

  } catch (error) {
    console.error('Error calculating impact:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin routes for managing recycling centers
router.post('/admin', [
  auth,
  body('name').isLength({ min: 2, max: 100 }).withMessage('Name must be 2-100 characters'),
  body('description').isLength({ min: 10, max: 500 }).withMessage('Description must be 10-500 characters'),
  body('location.county').notEmpty().withMessage('County is required'),
  body('location.town').notEmpty().withMessage('Town is required'),
  body('location.address').notEmpty().withMessage('Address is required'),
  body('contactInfo.phone').isMobilePhone().withMessage('Valid phone number required'),
  body('contactInfo.email').isEmail().withMessage('Valid email required'),
  body('specialties').isArray({ min: 1 }).withMessage('At least one specialty required'),
  body('acceptedItems').isArray({ min: 1 }).withMessage('At least one accepted item required'),
  body('rewardRate').isFloat({ min: 1, max: 30 }).withMessage('Reward rate must be 1-30 tokens per kg'),
  body('licenseNumber').notEmpty().withMessage('License number is required')
], async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation errors',
        errors: errors.array()
      });
    }

    // Check if license number already exists
    const existingCenter = await RecyclingCenter.findOne({
      licenseNumber: req.body.licenseNumber
    });

    if (existingCenter) {
      return res.status(400).json({
        success: false,
        message: 'License number already exists'
      });
    }

    const center = new RecyclingCenter({
      ...req.body,
      createdBy: req.user.id,
      isVerified: false,
      isActive: true
    });

    await center.save();

    res.status(201).json({
      success: true,
      message: 'Recycling center created successfully',
      data: center
    });

  } catch (error) {
    console.error('Error creating recycling center:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin get all recycling centers (including inactive)
router.get('/admin/all', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let filter = {};
    if (status === 'active') filter.isActive = true;
    if (status === 'inactive') filter.isActive = false;
    if (status === 'verified') filter.isVerified = true;
    if (status === 'unverified') filter.isVerified = false;

    const centers = await RecyclingCenter.find(filter)
      .populate('createdBy', 'firstName lastName')
      .populate('lastUpdatedBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await RecyclingCenter.countDocuments(filter);

    res.json({
      success: true,
      data: centers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching admin centers:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin update recycling center
router.patch('/admin/:id', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const center = await RecyclingCenter.findById(req.params.id);
    if (!center) {
      return res.status(404).json({
        success: false,
        message: 'Recycling center not found'
      });
    }

    // Update fields
    Object.keys(req.body).forEach(key => {
      if (key !== '_id' && key !== 'createdBy' && key !== 'createdAt') {
        center[key] = req.body[key];
      }
    });

    center.lastUpdatedBy = req.user.id;
    await center.save();

    res.json({
      success: true,
      message: 'Recycling center updated successfully',
      data: center
    });

  } catch (error) {
    console.error('Error updating recycling center:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Admin verify recycling center
router.patch('/admin/:id/verify', auth, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const { isVerified, adminNotes } = req.body;

    const center = await RecyclingCenter.findById(req.params.id);
    if (!center) {
      return res.status(404).json({
        success: false,
        message: 'Recycling center not found'
      });
    }

    center.isVerified = isVerified;
    if (adminNotes) center.adminNotes = adminNotes;
    center.lastUpdatedBy = req.user.id;

    await center.save();

    res.json({
      success: true,
      message: `Recycling center ${isVerified ? 'verified' : 'unverified'} successfully`,
      data: center
    });

  } catch (error) {
    console.error('Error verifying recycling center:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
