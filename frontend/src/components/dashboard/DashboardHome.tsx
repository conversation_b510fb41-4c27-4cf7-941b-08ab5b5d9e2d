'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import ClothingGrid from '@/components/clothing/ClothingGrid';
import {
  Coins,
  Shirt,
  Heart,
  Recycle,
  TrendingUp,
  Bot,
  ShoppingBag,
  Plus
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  images: string[];
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    rating: {
      average: number;
      count: number;
    };
  };
  location: {
    county: string;
    town: string;
  };
  createdAt: string;
}

interface User {
  id: string;
  phoneNumber: string;
  email?: string;
  firstName: string;
  lastName: string;
  fullName: string;
  profilePicture?: string;
  location: {
    county: string;
    town: string;
  };
  isVerified: boolean;
  role: string;
  pediTokens: number;
  rating: {
    average: number;
    count: number;
  };
  sustainabilityScore: number;
  totalDonations: number;
  totalSwaps: number;
  joinedAt: string;
  lastActive: string;
}

interface DashboardHomeProps {
  onNavigate: (page: string) => void;
}

export default function DashboardHome({ onNavigate }: DashboardHomeProps) {
  const [user, setUser] = useState<User | null>(null);
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }

    // Fetch clothing items
    fetchClothingItems();
  }, []);

  const fetchClothingItems = async () => {
    try {
      const response = await fetch('/api/clothing?limit=8&exchangeType=swap,token');
      if (response.ok) {
        const data = await response.json();
        setClothingItems(data.items || []);
      }
    } catch (error) {
      console.error('Error fetching clothing items:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user.firstName}! 👋
        </h2>
        <p className="text-gray-600">
          Ready to continue your sustainable fashion journey?
        </p>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-700">Pedi Tokens</CardTitle>
            <Coins className="h-5 w-5 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-600 mb-1">{user.pediTokens}</div>
            <p className="text-xs text-gray-500">
              Your sustainable currency
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Swaps</CardTitle>
            <Recycle className="h-4 w-4 text-[#01796F]" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-[#01796F]">{user.totalSwaps}</div>
            <p className="text-xs text-gray-500">
              Clothing exchanges completed
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Donations</CardTitle>
            <Heart className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">{user.totalDonations}</div>
            <p className="text-xs text-gray-500">
              Items donated to charity
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sustainability Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{user.sustainabilityScore}</div>
            <p className="text-xs text-gray-500">
              Environmental impact
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Available Items for Swap/Token Exchange */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Available Items</h3>
            <p className="text-gray-600">Discover clothing available for swap or token exchange</p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => router.push('/list-item')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              List Item
            </Button>
            <Button
              onClick={() => router.push('/browse')}
              className="flex items-center gap-2 bg-[#01796F] hover:bg-[#032221]"
            >
              <ShoppingBag className="h-4 w-4" />
              Browse All
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : (
          <ClothingGrid items={clothingItems} />
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => router.push('/ai-assistant')}>
          <CardContent className="p-6 text-center">
            <Bot className="h-8 w-8 mx-auto mb-3 text-purple-600" />
            <h3 className="font-medium text-gray-900 mb-1">AI Assistant</h3>
            <p className="text-sm text-gray-600">Get fashion advice</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => onNavigate('donations')}>
          <CardContent className="p-6 text-center">
            <Heart className="h-8 w-8 mx-auto mb-3 text-red-600" />
            <h3 className="font-medium text-gray-900 mb-1">Donate</h3>
            <p className="text-sm text-gray-600">Help charity & earn tokens</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => onNavigate('recycling')}>
          <CardContent className="p-6 text-center">
            <Recycle className="h-8 w-8 mx-auto mb-3 text-[#01796F]" />
            <h3 className="font-medium text-gray-900 mb-1">Recycle</h3>
            <p className="text-sm text-gray-600">Bulk recycling rewards</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
              onClick={() => onNavigate('tokens')}>
          <CardContent className="p-6 text-center">
            <Coins className="h-8 w-8 mx-auto mb-3 text-yellow-600" />
            <h3 className="font-medium text-gray-900 mb-1">Buy Tokens</h3>
            <p className="text-sm text-gray-600">Purchase Pedi tokens</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
