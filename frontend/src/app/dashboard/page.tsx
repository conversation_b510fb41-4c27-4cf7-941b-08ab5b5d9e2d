'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ClothingGrid from '@/components/clothing/ClothingGrid';
import {
  Coins,
  Shirt,
  Heart,
  Recycle,
  TrendingUp,
  Bot,
  ShoppingBag,
  Plus
} from 'lucide-react';

interface ClothingItem {
  _id: string;
  title: string;
  description: string;
  category: string;
  size: string;
  color: string;
  condition: string;
  exchangeType: string;
  tokenPrice?: number;
  originalPrice?: number;
  images: string[];
  views: number;
  likes: string[];
  likeCount: number;
  tags: string[];
  location: {
    county: string;
    town: string;
  };
  owner: {
    _id: string;
    firstName: string;
    lastName: string;
    profilePicture?: string;
    rating: number;
    sustainabilityScore: number;
  };
  createdAt: string;
  isAvailable: boolean;
}

export default function DashboardPage() {
  const [availableItems, setAvailableItems] = useState<ClothingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('token');
        const userData = localStorage.getItem('user');

        if (!token || !userData) {
          router.push('/auth');
          return;
        }

        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);

        // Fetch available clothing items for swap/token exchange
        const response = await fetch('/api/clothing?exchangeType=swap,token&limit=12', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setAvailableItems(data.data || []);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [router]);

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user.firstName}! 👋
          </h2>
          <p className="text-gray-600">
            Ready to continue your sustainable fashion journey?
          </p>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">Pedi Tokens</CardTitle>
              <Coins className="h-5 w-5 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-yellow-600 mb-1">{user.pediTokens}</div>
              <p className="text-xs text-gray-500">
                Your sustainable currency
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Swaps</CardTitle>
              <Recycle className="h-4 w-4 text-[#01796F]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-[#01796F]">{user.totalSwaps}</div>
              <p className="text-xs text-gray-500">
                Clothing exchanges completed
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Donations</CardTitle>
              <Heart className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{user.totalDonations}</div>
              <p className="text-xs text-gray-500">
                Items donated to charity
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Sustainability Score</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{user.sustainabilityScore}</div>
              <p className="text-xs text-gray-500">
                Your eco-impact rating
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Available Items for Swap/Token Exchange */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Available Items</h3>
              <p className="text-gray-600">Discover clothing available for swap or token exchange</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => router.push('/list-item')}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                List Item
              </Button>
              <Button
                onClick={() => router.push('/browse')}
                className="flex items-center gap-2 bg-[#01796F] hover:bg-[#032221]"
              >
                <ShoppingBag className="h-4 w-4" />
                Browse All
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#01796F]"></div>
            </div>
          ) : (
            <ClothingGrid
              items={availableItems}
              loading={loading}
              currentUserId={user.id}
            />
          )}

          {availableItems.length === 0 && !loading && (
            <Card className="text-center py-12">
              <CardContent>
                <Shirt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No items available</h3>
                <p className="text-gray-600 mb-4">
                  Be the first to list an item for swap or token exchange!
                </p>
                <Button
                  onClick={() => router.push('/list-item')}
                  className="bg-[#01796F] hover:bg-[#032221]"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  List Your First Item
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
                onClick={() => router.push('/ai-assistant')}>
            <CardContent className="p-6 text-center">
              <Bot className="h-8 w-8 mx-auto mb-3 text-purple-600" />
              <h3 className="font-medium text-gray-900 mb-1">AI Assistant</h3>
              <p className="text-sm text-gray-600">Get fashion advice</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
                onClick={() => router.push('/dashboard/donations')}>
            <CardContent className="p-6 text-center">
              <Heart className="h-8 w-8 mx-auto mb-3 text-red-600" />
              <h3 className="font-medium text-gray-900 mb-1">Donate</h3>
              <p className="text-sm text-gray-600">Help charity & earn tokens</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
                onClick={() => router.push('/dashboard/recycling')}>
            <CardContent className="p-6 text-center">
              <Recycle className="h-8 w-8 mx-auto mb-3 text-[#01796F]" />
              <h3 className="font-medium text-gray-900 mb-1">Recycle</h3>
              <p className="text-sm text-gray-600">Bulk recycling rewards</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer"
                onClick={() => router.push('/dashboard/tokens')}>
            <CardContent className="p-6 text-center">
              <Coins className="h-8 w-8 mx-auto mb-3 text-yellow-600" />
              <h3 className="font-medium text-gray-900 mb-1">Buy Tokens</h3>
              <p className="text-sm text-gray-600">Purchase with M-Pesa</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
