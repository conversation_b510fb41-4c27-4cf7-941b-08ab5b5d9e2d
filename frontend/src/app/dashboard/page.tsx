'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';

// Import dashboard page components
import DashboardHome from '@/components/dashboard/DashboardHome';
import MyListingsPage from '@/components/dashboard/MyListingsPage';
import TokensPage from '@/components/dashboard/TokensPage';
import DonationsPage from '@/components/dashboard/DonationsPage';
import RecyclingPage from '@/components/dashboard/RecyclingPage';
import MessagesPage from '@/components/dashboard/MessagesPage';
import ProfilePage from '@/components/dashboard/ProfilePage';



export default function DashboardPage() {
  const [currentPage, setCurrentPage] = useState('home');
  const searchParams = useSearchParams();

  useEffect(() => {
    // Check URL parameters for initial page
    const page = searchParams?.get('page');
    if (page) {
      setCurrentPage(page);
    }
  }, [searchParams]);

  const handleNavigate = (page: string) => {
    setCurrentPage(page);
    // Update URL without full page reload
    const url = new URL(window.location.href);
    url.searchParams.set('page', page);
    window.history.pushState({}, '', url.toString());
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'my-listings':
        return <MyListingsPage />;
      case 'tokens':
        return <TokensPage />;
      case 'donations':
        return <DonationsPage />;
      case 'recycling':
        return <RecyclingPage />;
      case 'messages':
        return <MessagesPage />;
      case 'profile':
        return <ProfilePage />;
      default:
        return <DashboardHome onNavigate={handleNavigate} />;
    }
  };

  return (
    <DashboardLayout currentPage={currentPage} onNavigate={handleNavigate}>
      {renderCurrentPage()}
    </DashboardLayout>
  );
}
